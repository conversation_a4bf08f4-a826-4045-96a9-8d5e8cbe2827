{"name": "linkify-it", "version": "4.0.1", "description": "Links recognition library with FULL unicode support", "keywords": ["linkify", "linkifier", "autolink", "autolinker"], "repository": "markdown-it/linkify-it", "files": ["index.js", "lib/"], "license": "MIT", "scripts": {"lint": "eslint .", "test": "npm run lint && nyc mocha", "coverage": "npm run test && nyc report --reporter html", "report-coveralls": "nyc --reporter=lcov mocha", "demo": "npm run lint && node support/build_demo.js", "doc": "node support/build_doc.js", "gh-pages": "npm run demo && npm run doc && shx cp -R doc/ demo/ && gh-pages -d demo -f", "prepublishOnly": "npm run gh-pages"}, "dependencies": {"uc.micro": "^1.0.1"}, "devDependencies": {"ansi": "^0.3.0", "autoprefixer-stylus": "^1.0.0", "benchmark": "^2.1.0", "browserify": "^17.0.0", "eslint": "^7.0.0", "gh-pages": "^3.2.3", "mdurl": "^1.0.0", "mocha": "^9.1.2", "ndoc": "^6.0.0", "nyc": "^15.0.1", "pug-cli": "^1.0.0-alpha6", "shelljs": "^0.8.4", "shx": "^0.3.2", "stylus": "~0.55.0", "tlds": "^1.166.0"}}