{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-content.tsx"], "sourcesContent": ["'use client'\n\nimport { useMemo, useEffect } from 'react'\nimport MarkdownIt from 'markdown-it'\nimport hljs from 'highlight.js'\nimport '@uiw/react-markdown-preview/markdown.css'\nimport 'highlight.js/styles/github-dark.css'\n\ninterface MarkdownContentProps {\n  content: string\n  className?: string\n  enableHtml?: boolean\n  enableBreaks?: boolean\n  enableTypographer?: boolean\n}\n\nexport default function MarkdownContent({\n  content,\n  className = '',\n  enableHtml = true,\n  enableBreaks = false,\n  enableTypographer = true\n}: MarkdownContentProps) {\n  const processedContent = useMemo(() => {\n    // Initialize markdown-it with options\n    const md = new MarkdownIt({\n      html: enableHtml,        // Enable HTML tags in source\n      linkify: true,          // Autoconvert URL-like text to links\n      typographer: enableTypographer, // Enable some language-neutral replacement + quotes beautification\n      breaks: enableBreaks    // Convert '\\n' in paragraphs into <br>\n    })\n\n    // Render markdown to HTML\n    return md.render(content)\n  }, [content, enableHtml, enableBreaks, enableTypographer])\n\n  // Initialize highlight.js after content is rendered\n  useEffect(() => {\n    hljs.highlightAll()\n  }, [processedContent])\n\n  return (\n    <div\n      className={`prose prose-base sm:prose-lg max-w-none dark:prose-invert markdown-renderer enhanced-markdown-content ${className}`}\n      data-color-mode=\"auto\"\n      dangerouslySetInnerHTML={{ __html: processedContent }}\n      style={{\n        backgroundColor: 'transparent',\n        color: 'inherit'\n      }}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;;;AAgBe,SAAS,gBAAgB,EACtC,OAAO,EACP,YAAY,EAAE,EACd,aAAa,IAAI,EACjB,eAAe,KAAK,EACpB,oBAAoB,IAAI,EACH;IACrB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,sCAAsC;QACtC,MAAM,KAAK,IAAI,uIAAA,CAAA,UAAU,CAAC;YACxB,MAAM;YACN,SAAS;YACT,aAAa;YACb,QAAQ,aAAgB,uCAAuC;QACjE;QAEA,0BAA0B;QAC1B,OAAO,GAAG,MAAM,CAAC;IACnB,GAAG;QAAC;QAAS;QAAY;QAAc;KAAkB;IAEzD,oDAAoD;IACpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8JAAA,CAAA,UAAI,CAAC,YAAY;IACnB,GAAG;QAAC;KAAiB;IAErB,qBACE,8OAAC;QACC,WAAW,CAAC,sGAAsG,EAAE,WAAW;QAC/H,mBAAgB;QAChB,yBAAyB;YAAE,QAAQ;QAAiB;QACpD,OAAO;YACL,iBAAiB;YACjB,OAAO;QACT;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/table-of-contents.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { TOCItem } from '@/lib/toc'\n\ninterface TableOfContentsProps {\n  toc: TOCItem[]\n  className?: string\n}\n\ninterface TOCItemComponentProps {\n  item: TOCItem\n  activeId: string\n  onItemClick: (id: string) => void\n}\n\nfunction TOCItemComponent({ item, activeId, onItemClick }: TOCItemComponentProps) {\n  const isActive = activeId === item.id\n  const hasActiveChild = item.children.some(child => \n    child.id === activeId || child.children.some(grandchild => grandchild.id === activeId)\n  )\n\n  return (\n    <li>\n      <a\n        href={`#${item.id}`}\n        onClick={(e) => {\n          e.preventDefault()\n          onItemClick(item.id)\n        }}\n        className={`\n          block py-1 px-2 text-sm rounded-md transition-all duration-200 hover:bg-muted/50\n          ${isActive \n            ? 'text-primary bg-primary/10 font-medium border-l-2 border-primary' \n            : hasActiveChild\n            ? 'text-foreground/80'\n            : 'text-muted-foreground hover:text-foreground'\n          }\n        `}\n        style={{ paddingLeft: `${(item.level - 1) * 12 + 8}px` }}\n      >\n        {item.title}\n      </a>\n      {item.children.length > 0 && (\n        <ul className=\"mt-1\">\n          {item.children.map((child) => (\n            <TOCItemComponent\n              key={child.id}\n              item={child}\n              activeId={activeId}\n              onItemClick={onItemClick}\n            />\n          ))}\n        </ul>\n      )}\n    </li>\n  )\n}\n\nexport default function TableOfContents({ toc, className = '' }: TableOfContentsProps) {\n  const [activeId, setActiveId] = useState('')\n  const [isOpen, setIsOpen] = useState(false)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      (entries) => {\n        // Find the entry that's most visible\n        let maxRatio = 0\n        let mostVisibleId = ''\n\n        entries.forEach((entry) => {\n          if (entry.isIntersecting && entry.intersectionRatio > maxRatio) {\n            maxRatio = entry.intersectionRatio\n            mostVisibleId = entry.target.id\n          }\n        })\n\n        if (mostVisibleId) {\n          setActiveId(mostVisibleId)\n        }\n      },\n      {\n        rootMargin: '-20% 0% -35% 0%',\n        threshold: [0, 0.25, 0.5, 0.75, 1]\n      }\n    )\n\n    // Observe all headings\n    const headings = document.querySelectorAll('h1[id], h2[id], h3[id], h4[id], h5[id], h6[id]')\n    headings.forEach((heading) => observer.observe(heading))\n\n    return () => observer.disconnect()\n  }, [])\n\n  const handleItemClick = (id: string) => {\n    const element = document.getElementById(id)\n    if (element) {\n      const yOffset = -80 // Account for any fixed headers\n      const y = element.getBoundingClientRect().top + window.pageYOffset + yOffset\n      \n      window.scrollTo({ top: y, behavior: 'smooth' })\n      \n      // Update URL hash\n      window.history.replaceState(null, '', `#${id}`)\n      \n      // Close mobile menu\n      setIsOpen(false)\n    }\n  }\n\n  if (toc.length === 0) {\n    return null\n  }\n\n  return (\n    <>\n      {/* Mobile TOC Toggle Button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"lg:hidden fixed top-20 right-4 z-40 bg-card border border-border rounded-lg p-2 shadow-lg hover:bg-muted transition-colors\"\n        aria-label=\"Toggle table of contents\"\n      >\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n        </svg>\n      </button>\n\n      {/* Mobile TOC Overlay */}\n      {isOpen && (\n        <div\n          className=\"lg:hidden fixed inset-0 z-30 bg-black/50\"\n          onClick={() => setIsOpen(false)}\n        />\n      )}\n\n      {/* TOC Sidebar */}\n      <div className={`\n        ${className}\n        lg:block lg:sticky lg:top-24 lg:h-fit lg:max-h-[calc(100vh-6rem)]\n        ${isOpen ? 'block' : 'hidden'}\n        lg:relative lg:z-auto lg:bg-transparent lg:border-0 lg:shadow-none lg:p-0\n        fixed top-20 right-4 z-40 bg-card border border-border rounded-xl shadow-xl p-4 max-h-[70vh] w-80 max-w-[calc(100vw-2rem)]\n      `}>\n        <div className=\"flex items-center justify-between mb-4 lg:mb-6\">\n          <h3 className=\"font-semibold text-foreground text-sm lg:text-base\">\n            Table of Contents\n          </h3>\n          <button\n            onClick={() => setIsOpen(false)}\n            className=\"lg:hidden p-1 hover:bg-muted rounded\"\n            aria-label=\"Close table of contents\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        <nav className=\"overflow-y-auto lg:max-h-[calc(100vh-12rem)] max-h-[60vh]\">\n          <ul className=\"space-y-1\">\n            {toc.map((item) => (\n              <TOCItemComponent\n                key={item.id}\n                item={item}\n                activeId={activeId}\n                onItemClick={handleItemClick}\n              />\n            ))}\n          </ul>\n        </nav>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAgBA,SAAS,iBAAiB,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAyB;IAC9E,MAAM,WAAW,aAAa,KAAK,EAAE;IACrC,MAAM,iBAAiB,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAA,QACxC,MAAM,EAAE,KAAK,YAAY,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAA,aAAc,WAAW,EAAE,KAAK;IAG/E,qBACE,8OAAC;;0BACC,8OAAC;gBACC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBACnB,SAAS,CAAC;oBACR,EAAE,cAAc;oBAChB,YAAY,KAAK,EAAE;gBACrB;gBACA,WAAW,CAAC;;UAEV,EAAE,WACE,qEACA,iBACA,uBACA,8CACH;QACH,CAAC;gBACD,OAAO;oBAAE,aAAa,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC;gBAAC;0BAEtD,KAAK,KAAK;;;;;;YAEZ,KAAK,QAAQ,CAAC,MAAM,GAAG,mBACtB,8OAAC;gBAAG,WAAU;0BACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,sBAClB,8OAAC;wBAEC,MAAM;wBACN,UAAU;wBACV,aAAa;uBAHR,MAAM,EAAE;;;;;;;;;;;;;;;;AAU3B;AAEe,SAAS,gBAAgB,EAAE,GAAG,EAAE,YAAY,EAAE,EAAwB;IACnF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC;YACC,qCAAqC;YACrC,IAAI,WAAW;YACf,IAAI,gBAAgB;YAEpB,QAAQ,OAAO,CAAC,CAAC;gBACf,IAAI,MAAM,cAAc,IAAI,MAAM,iBAAiB,GAAG,UAAU;oBAC9D,WAAW,MAAM,iBAAiB;oBAClC,gBAAgB,MAAM,MAAM,CAAC,EAAE;gBACjC;YACF;YAEA,IAAI,eAAe;gBACjB,YAAY;YACd;QACF,GACA;YACE,YAAY;YACZ,WAAW;gBAAC;gBAAG;gBAAM;gBAAK;gBAAM;aAAE;QACpC;QAGF,uBAAuB;QACvB,MAAM,WAAW,SAAS,gBAAgB,CAAC;QAC3C,SAAS,OAAO,CAAC,CAAC,UAAY,SAAS,OAAO,CAAC;QAE/C,OAAO,IAAM,SAAS,UAAU;IAClC,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,MAAM,UAAU,CAAC,GAAG,gCAAgC;;YACpD,MAAM,IAAI,QAAQ,qBAAqB,GAAG,GAAG,GAAG,OAAO,WAAW,GAAG;YAErE,OAAO,QAAQ,CAAC;gBAAE,KAAK;gBAAG,UAAU;YAAS;YAE7C,kBAAkB;YAClB,OAAO,OAAO,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,CAAC,EAAE,IAAI;YAE9C,oBAAoB;YACpB,UAAU;QACZ;IACF;IAEA,IAAI,IAAI,MAAM,KAAK,GAAG;QACpB,OAAO;IACT;IAEA,qBACE;;0BAEE,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,cAAW;0BAEX,cAAA,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;YAKxE,wBACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;0BAK7B,8OAAC;gBAAI,WAAW,CAAC;QACf,EAAE,UAAU;;QAEZ,EAAE,SAAS,UAAU,SAAS;;;MAGhC,CAAC;;kCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,8OAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAU;gCACV,cAAW;0CAEX,cAAA,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAK3E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCACX,IAAI,GAAG,CAAC,CAAC,qBACR,8OAAC;oCAEC,MAAM;oCACN,UAAU;oCACV,aAAa;mCAHR,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAW5B", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport { Moon, Sun } from 'lucide-react'\nimport { useTheme } from 'next-themes'\n\nexport function ThemeToggle() {\n  const { theme, setTheme } = useTheme()\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <button className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\">\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n        <span className=\"sr-only\">切換主題</span>\n      </button>\n    )\n  }\n\n  return (\n    <button\n      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}\n      className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\"\n    >\n      {theme === 'light' ? (\n        <Moon className=\"h-[1.2rem] w-[1.2rem]\" />\n      ) : (\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n      )}\n      <span className=\"sr-only\">切換主題</span>\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAO,WAAU;;8BAChB,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;;IAGhC;IAEA,qBACE,8OAAC;QACC,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAU;;YAET,UAAU,wBACT,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;qCAEhB,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;0BAEjB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/client-theme-toggle.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { ThemeToggle } from './theme-toggle'\r\n\r\nexport function ClientThemeToggle() {\r\n  return <ThemeToggle />\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,qBAAO,8OAAC,qIAAA,CAAA,cAAW;;;;;AACrB", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/admin-actions.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useRouter } from 'next/navigation'\r\nimport { createClient } from '@/lib/supabase/client'\r\nimport Link from 'next/link'\r\n\r\nexport function AdminActions({ postId }: { postId: string }) {\r\n  const router = useRouter()\r\n  const supabase = createClient()\r\n\r\n  const handleDelete = async () => {\r\n    if (!confirm('Are you sure you want to delete this post? This action cannot be undone.')) return\r\n\r\n    const { error } = await supabase\r\n      .from('posts')\r\n      .delete()\r\n      .eq('id', postId)\r\n\r\n    if (error) {\r\n      alert('Error deleting post: ' + error.message)\r\n    } else {\r\n      alert('Post deleted successfully!')\r\n      router.push('/')\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex flex-col sm:flex-row gap-3\">\r\n      <Link\r\n        href={`/admin/edit-post/${postId}`}\r\n        className=\"inline-flex items-center justify-center bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-all duration-200 font-medium shadow-sm hover:shadow-md\"\r\n      >\r\n        <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\r\n        </svg>\r\n        Edit Post\r\n      </Link>\r\n      <button\r\n        className=\"inline-flex items-center justify-center bg-destructive text-destructive-foreground px-4 py-2 rounded-lg hover:bg-destructive/90 transition-all duration-200 font-medium shadow-sm hover:shadow-md\"\r\n        onClick={handleDelete}\r\n      >\r\n        <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\r\n        </svg>\r\n        Delete Post\r\n      </button>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMO,SAAS,aAAa,EAAE,MAAM,EAAsB;IACzD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ,6EAA6E;QAE1F,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,SACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO;YACT,MAAM,0BAA0B,MAAM,OAAO;QAC/C,OAAO;YACL,MAAM;YACN,OAAO,IAAI,CAAC;QACd;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4JAAA,CAAA,UAAI;gBACH,MAAM,CAAC,iBAAiB,EAAE,QAAQ;gBAClC,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACtE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBACjE;;;;;;;0BAGR,8OAAC;gBACC,WAAU;gBACV,SAAS;;kCAET,8OAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACtE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBACjE;;;;;;;;;;;;;AAKd", "debugId": null}}, {"offset": {"line": 639, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/back-to-top.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\nexport function BackToTop() {\n  const [isVisible, setIsVisible] = useState(false)\n\n  useEffect(() => {\n    const toggleVisibility = () => {\n      if (window.pageYOffset > 300) {\n        setIsVisible(true)\n      } else {\n        setIsVisible(false)\n      }\n    }\n\n    window.addEventListener('scroll', toggleVisibility)\n\n    return () => window.removeEventListener('scroll', toggleVisibility)\n  }, [])\n\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    })\n  }\n\n  if (!isVisible) {\n    return null\n  }\n\n  return (\n    <button\n      onClick={scrollToTop}\n      className=\"fixed bottom-8 right-8 z-50 inline-flex items-center justify-center w-12 h-12 bg-primary text-primary-foreground rounded-full shadow-lg hover:shadow-xl transition-all duration-200 group hover:scale-110\"\n      aria-label=\"Back to top\"\n    >\n      <svg className=\"w-5 h-5 group-hover:-translate-y-1 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 10l7-7m0 0l7 7m-7-7v18\" />\n      </svg>\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI,OAAO,WAAW,GAAG,KAAK;gBAC5B,aAAa;YACf,OAAO;gBACL,aAAa;YACf;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,WAAU;QACV,cAAW;kBAEX,cAAA,8OAAC;YAAI,WAAU;YAA0D,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjH,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;;;;;;AAI7E", "debugId": null}}, {"offset": {"line": 705, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/post-date.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\n\r\ninterface PostDateProps {\r\n  date: string\r\n  isUpdateDate?: boolean\r\n}\r\n\r\nexport function PostDate({ date, isUpdateDate = false }: PostDateProps) {\r\n  const [isMobile, setIsMobile] = useState(false)\r\n  const [mounted, setMounted] = useState(false)\r\n\r\n  useEffect(() => {\r\n    setMounted(true)\r\n    const checkWidth = () => {\r\n      setIsMobile(window.innerWidth < 640)\r\n    }\r\n\r\n    // Initial check\r\n    checkWidth()\r\n\r\n    // Add resize listener\r\n    window.addEventListener('resize', checkWidth)\r\n\r\n    // Cleanup\r\n    return () => window.removeEventListener('resize', checkWidth)\r\n  }, [])\r\n\r\n  // Prevent hydration mismatch by showing consistent content until mounted\r\n  if (!mounted) {\r\n    return (\r\n      <span className=\"flex items-center text-muted-foreground bg-muted/50 px-2 sm:px-3 py-1 sm:py-2 rounded-full text-xs sm:text-sm\">\r\n        {isUpdateDate && (\r\n          <>\r\n            <svg className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\r\n            </svg>\r\n            <span className=\"hidden sm:inline\">Updated </span>\r\n          </>\r\n        )}\r\n        {new Date(date).toLocaleDateString('en-US', {\r\n          month: 'short',\r\n          day: 'numeric',\r\n          year: 'numeric'\r\n        })}\r\n      </span>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <span className=\"flex items-center text-muted-foreground bg-muted/50 px-2 sm:px-3 py-1 sm:py-2 rounded-full text-xs sm:text-sm\">\r\n      {isUpdateDate && (\r\n        <>\r\n          <svg className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\r\n          </svg>\r\n          <span className=\"hidden sm:inline\">Updated </span>\r\n        </>\r\n      )}\r\n      {new Date(date).toLocaleDateString('en-US', {\r\n        month: 'short',\r\n        day: 'numeric',\r\n        year: isMobile ? undefined : 'numeric'\r\n      })}\r\n    </span>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,SAAS,EAAE,IAAI,EAAE,eAAe,KAAK,EAAiB;IACpE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,MAAM,aAAa;YACjB,YAAY,OAAO,UAAU,GAAG;QAClC;QAEA,gBAAgB;QAChB;QAEA,sBAAsB;QACtB,OAAO,gBAAgB,CAAC,UAAU;QAElC,UAAU;QACV,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,yEAAyE;IACzE,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAK,WAAU;;gBACb,8BACC;;sCACE,8OAAC;4BAAI,WAAU;4BAAqC,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC5F,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;sCAEvE,8OAAC;4BAAK,WAAU;sCAAmB;;;;;;;;gBAGtC,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;oBAC1C,OAAO;oBACP,KAAK;oBACL,MAAM;gBACR;;;;;;;IAGN;IAEA,qBACE,8OAAC;QAAK,WAAU;;YACb,8BACC;;kCACE,8OAAC;wBAAI,WAAU;wBAAqC,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAC5F,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;kCAEvE,8OAAC;wBAAK,WAAU;kCAAmB;;;;;;;;YAGtC,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;gBAC1C,OAAO;gBACP,KAAK;gBACL,MAAM,WAAW,YAAY;YAC/B;;;;;;;AAGN", "debugId": null}}]}