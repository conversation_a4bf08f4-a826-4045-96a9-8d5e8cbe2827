// @ts-nocheck
/**
 * @import {Syntax} from '../core.js'
 */
import refractorClike from './clike.js'
processing.displayName = 'processing'
processing.aliases = []

/** @type {Syntax} */
export default function processing(Prism) {
  Prism.register(refractorClike)
  Prism.languages.processing = Prism.languages.extend('clike', {
    keyword:
      /\b(?:break|case|catch|class|continue|default|else|extends|final|for|if|implements|import|new|null|private|public|return|static|super|switch|this|try|void|while)\b/,
    // Spaces are allowed between function name and parenthesis
    function: /\b\w+(?=\s*\()/,
    operator: /<[<=]?|>[>=]?|&&?|\|\|?|[%?]|[!=+\-*\/]=?/
  })
  Prism.languages.insertBefore('processing', 'number', {
    // Special case: XML is a type
    constant: /\b(?!XML\b)[A-Z][A-Z\d_]+\b/,
    type: {
      pattern: /\b(?:boolean|byte|char|color|double|float|int|[A-Z]\w*)\b/,
      alias: 'class-name'
    }
  })
}
